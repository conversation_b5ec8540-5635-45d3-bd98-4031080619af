---
# Override main shell translations for PLG experience
cluster:
  credential:
    banner:
      createCredential: |-
        {length, plural,
          =0 {Let's get you connected! We'll help you set up your cloud account}
          other {Great! Let's add another cloud account}
        }

# PLG Wizard specific translations
plg:
  wizard:
    steps:
      clusterBasics:
        title: "Let's create your EKS cluster"
        subtitle: "First, we need a name for your cluster and AWS credentials."
        clusterName: "Cluster Name"
        description: "Description (Optional)"
        descriptionPlaceholder: "Production cluster for web services"
      nodeConfiguration:
        title: "Configure your worker nodes"
        subtitle: "We've selected recommended settings that work well for most workloads."
      review:
        title: "Review your cluster configuration"
        advancedToggle:
          title: "Need more control?"
          subtitle: "Access advanced settings for networking, logging, IAM, and more."
          button: "Show Advanced Settings"
    configSummary:
      sections:
        cluster: "Cluster Configuration"
        nodes: "Node Configuration"
        cost: "Estimated Cost"
      fields:
        clusterName: "Cluster Name"
        awsRegion: "AWS Region"
        kubernetesVersion: "Kubernetes Version"
        networkAccess: "Network Access"
        instanceType: "Instance Type"
        diskSize: "Disk Size"
        autoScaling: "Auto-scaling"
        initialSize: "Initial Size"
        monthlyCost: "Monthly estimate:"
        costDisclaimer: "* This is a rough estimate based on instance types and does not include data transfer, storage, or other AWS services."
      values:
        notSet: "Not set"
        latestStable: "Latest stable"
        publicNetwork: "Public"
        privateNetwork: "Private"
        publicPrivateNetwork: "Public and Private"
        defaultPublicNetwork: "Default (Public)"
        nodeGroupsConfigured: "{count} node groups configured"
        diskSizeUnit: "{size} GB"
        autoScalingRange: "{min} - {max} nodes"
        nodeCount: "{count} nodes"
      readyBanner:
        title: "Ready to create your cluster!"
        subtitle: "Your cluster will be created with production-ready defaults including:"
        features:
          - "Automatic security updates"
          - "Network isolation and security groups"
          - "CloudWatch logging enabled"
          - "IAM roles for service accounts (IRSA)"
    simpleNodeGroup:
      sections:
        instanceType:
          title: "Instance Type"
          subtitle: "Select the computing power for your worker nodes"
          label: "Instance Type"
          costEstimate: "Estimated cost: {cost}"
        clusterSize:
          title: "Cluster Size"
          subtitle: "Configure auto-scaling for your cluster"
          minNodes: "Minimum Nodes"
          desiredNodes: "Desired Nodes"
          maxNodes: "Maximum Nodes"
          autoScaleInfo: "Your cluster will automatically scale between {min} and {max} nodes based on workload."
        storage:
          title: "Storage"
          subtitle: "Disk space for each node"
          diskSize: "Disk Size"

eks:
  accordionHeaders:
    cluster: Cluster Options
    clusterAgent: Cluster Agent Configuration
    fleetAgent: Fleet Agent Configuration
    logging: Logging
    networking: Networking
  api:
    label: API
    tooltip: Your cluster's API server is the control plane component that exposes the Kubernetes API.
  audit:
    label: Audit
    tooltip: Kubernetes audit logs provide a record of the individual users, administrators, or system components that have affected your cluster.
  authenticator:
    label: Authenticator
    tooltip: Authenticator logs are unique to Amazon EKS. These logs represent the control plane component that Amazon EKS uses for Kubernetes Role Based Access Control (RBAC) authentication using IAM credentials.
  clusterName:
    label: Cluster Name
  controllerManager:
    label: Controller Manager
    tooltip: The controller manager manages the core control loops that are shipped with Kubernetes.
  defaultCreateOne: Default (One will be created automatically)
  ebsCSIDriver:
    label: Out of tree EBS CSI Driver
  enableNetworkPolicy:
    label: Project Network Isolation
  encryptSecrets:
    label: Encrypt secrets with a KMS key
  errors:
    fetchingRegions: 'Error fetching regions: {err}'
    greaterThanZero: '{key} must be greater than zero.'
    atLeastZero: '{key} must be at least zero.'
    minimumSubnets: You must use at least two subnets, and they should be in different availability zones.
    nodeGroups:
      nameUnique: Node group names must be unique within a cluster.
    publicOrPrivate: At least one of public or private access must be enabled.
    minMaxDesired: The node group desired size may not be less than the minimum size nor greater than the maximum size.
    minLessThanMax: The node group minimum size may not be greater than the maximum size.
    nodeGroupsRequired: Clusters must have at least one node group.
  import:
    label: Cluster to Register
  label: Amazon EKS
  nodeGroups:
    desiredSize:
      label: Desired ASG Size
    diskSize:
      label: Node Volume Size
    ec2SshKey:
      label: SSH Key
    gpu:
      label: GPU Enabled Instance
      tooltip: This setting is ignored when using a launch template with a custom AMI defined.
    groupDetails: Group Details
    groupLabels:
      label: Group Labels
    groupTags:
      label: Group Tags
    imageId:
      label: Amazon Machine Image ID
      tooltip: Custom AMIs is an advanced use case. We highly recommended that you read and fully understand the requirements for creating custom EKS AMIs in the <a aria-label="Amazon guide to using custom AMIs in EKS" href="https://aws.amazon.com/premiumsupport/knowledge-center/eks-custom-linux-ami/" target="_blank">AWS documentation.</a>
    instanceType:
      label: Instance Type
      tooltip: Instance Type will not be sent when requesting spot instances. You must include Spot Instance Types instead.
    kubernetesVersion:
      clusterWillUpgrade: A new control plane Kubernetes version has been selected. Once that upgrade is complete, you can come back and upgrade the node group.
      label: Node Kubernetes Version
      upgrade: Upgrade the node Kubernetes version from {from} to {to}
    launchTemplate:
      label: Launch Template
      rancherManaged: '{name} (Rancher-managed)'
      version: Template Version
    maxSize:
      label: Maximum ASG Size
    minSize:
      label: Minimum ASG Size
    name:
      label: Node Group Name
    nodeRole:
      label: Node Instance Role
    requestSpotInstances:
      label: Request Spot Instances
      warning: Amazon recommends selecting multiple instance types when using spot instances. Since the template you have selected allows for only one instance time, your nodes may be interrupted more often.
    resourceTags:
      label: Instance Resource Tags
    spotInstanceTypes:
      label: Spot Instance Types
    templateDetails: Template Details
    title: Node Groups
    unnamed: Unnamed Node Group
    userData:
      label: User Data
      tooltip: Pass user data to <a href="https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html#launch-template-user-data" target="_blank">Amazon using cloud-init</a> to automate common operations.
  privateAccess:
    label: Private Access
  publicAccess:
    label: Public Access
    tooltip: >-
      Optionally limit access to the public endpoint via explicit CIDR blocks. If you limit access to specific CIDR blocks, then it is recommended that you also enable private access to avoid losing network communication to the cluster. Configuring Public/Private API access is an advanced use case. Users are encouraged to read the EKS cluster endpoint access control <a href="https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html" target="_blank"  rel="nofollow noopener
      noreferrer">documentation.</a>
  publicAccessSources:
    addEndpoint: Add Endpoint
    label: Public Access Endpoints
  region:
    label: Region
  scheduler:
    label: Scheduler
    tooltip: The scheduler component manages when and where to run pods in your cluster.
  securityGroups:
    label: Additional Security Groups
    tooltip: EKS creates a security group for your cluster by default. You may choose additional security groups that will be applied to your EKS cluster. Only the default security group will be set on the nodes unless the launch template specified contains security groups to override them.
  serviceRole:
    label: Service Role
    options:
      custom: 'Custom: Choose from an existing service role'
      standard: 'Standard: A service role will be automatically created'
  subnets:
    default: Create a new vpc and subnet automatically
    title: VPCs and Subnets
    useCustom: Select from existing subnets
  tags:
    label: Tags
  version:
    label: Kubernetes Version
    upgradeWarning: (minor version >1 not allowed by EKS)
    upgradeDisallowed: "EKS only allows upgrades of one minor version: you must upgrade all node group versions before upgrading the cluster version."
  vpcSubnet:
    label: VPC and Subnet