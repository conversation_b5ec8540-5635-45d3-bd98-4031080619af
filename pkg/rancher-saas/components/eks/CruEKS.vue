<script lang='ts'>
import { mapGetters, Store } from 'vuex';
import { defineComponent } from 'vue';
import cloneDeep from 'lodash/cloneDeep';

import { removeObject } from '@shell/utils/array';
import { _CREATE, _EDIT, _VIEW } from '@shell/config/query-params';
import { NORMAN } from '@shell/config/types';
import { diffUpstreamSpec, syncUpstreamConfig } from '@shell/utils/kontainer';
import CreateEditView from '@shell/mixins/create-edit-view';
import FormValidation from '@shell/mixins/form-validation';

import CruResource from '@shell/components/CruResource.vue';
import LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';
import AgentConfiguration from '@shell/edit/provisioning.cattle.io.cluster/tabs/AgentConfiguration.vue';
import Labels from '@shell/components/form/Labels.vue';
import Tab from '@shell/components/Tabbed/Tab.vue';
import Tabbed from '@shell/components/Tabbed/index.vue';
import Accordion from '@components/Accordion/Accordion.vue';
import Banner from '@components/Banner/Banner.vue';
import ClusterMembershipEditor, { canViewClusterMembershipEditor } from '@shell/components/form/Members/ClusterMembershipEditor.vue';
import Loading from '@shell/components/Loading.vue';
import Wizard from '@shell/components/Wizard.vue';
import Card from '@components/Card/Card.vue';


import { EKSConfig, EKSNodeGroup, AWS, NormanCluster } from '../../types';
import NodeGroup from './NodeGroup.vue';
import Logging from './Logging.vue';
import Config from './Config.vue';
import Networking from './Networking.vue';
import AccountAccess from './AccountAccess.vue';
import Import from './Import.vue';
import SimpleNodeGroup from './SimpleNodeGroup.vue';
import ConfigSummary from './ConfigSummary.vue';

import EKSValidators from '../../util/validators';
import { CREATOR_PRINCIPAL_ID } from '@shell/config/labels-annotations';

const DEFAULT_CLUSTER = {
  dockerRootDir: '/var/lib/docker',
  enableClusterAlerting: false,
  enableClusterMonitoring: false,
  enableNetworkPolicy: false,
  labels: {},
  annotations: {},
  windowsPreferedCluster: false,
  fleetAgentDeploymentCustomization: {},
  clusterAgentDeploymentCustomization: {}
};

const DEFAULT__IMPORT_CLUSTER = {
  dockerRootDir: '/var/lib/docker',
  enableNetworkPolicy: false,
  labels: {},
  annotations: {},
  windowsPreferedCluster: false,
  fleetAgentDeploymentCustomization: {},
  clusterAgentDeploymentCustomization: {},
  eksConfig: {
    amazonCredentialSecret: '',
    displayName: '',
    imported: true,
    region: ''
  }
};

export const DEFAULT_REGION = 'us-west-2';

export const DEFAULT_NODE_GROUP_CONFIG = {
  desiredSize: 2,
  diskSize: 20,
  ec2SshKey: '',
  gpu: false,
  imageId: null,
  instanceType: 't3.medium',
  labels: {},
  maxSize: 2,
  minSize: 2,
  nodegroupName: '',
  nodeRole: '',
  requestSpotInstances: false,
  resourceTags: {},
  spotInstanceTypes: [],
  subnets: [],
  tags: {},
  type: 'nodeGroup',
  userData: '',
  _isNew: true,
};

export const DEFAULT_EKS_CONFIG = {
  publicAccess: true,
  privateAccess: false,
  publicAccessSources: [],
  secretsEncryption: false,
  securityGroups: [],
  tags: {},
  subnets: [],
  loggingTypes: [],
};

export default defineComponent({
  name: 'CruEKS',

  components: {
    CruResource,
    AccountAccess,
    NodeGroup,
    Logging,
    Config,
    Networking,
    LabeledInput,
    ClusterMembershipEditor,
    Labels,
    Tabbed,
    Tab,
    Accordion,
    Banner,
    AgentConfiguration,
    Loading,
    Import,
    Wizard,
    Card,
    SimpleNodeGroup,
    ConfigSummary
  },

  mixins: [CreateEditView, FormValidation],

  props: {
    mode: {
      type: String,
      default: _CREATE
    },

    // v2 provisioning cluster object
    value: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },

  async fetch() {
    const store = this.$store as Store<any>;

    if (this.value.id) {
      const liveNormanCluster = await this.value.findNormanCluster();

      this.normanCluster = await store.dispatch(`rancher/clone`, { resource: liveNormanCluster });
      // ensure any fields editable through this UI that have been altered in aws are shown here - see syncUpstreamConfig jsdoc for details
      if (!this.isNewOrUnprovisioned) {
        syncUpstreamConfig('eks', this.normanCluster);
      }
      // track original version on edit to ensure we don't offer k8s downgrades
      this.originalVersion = this.normanCluster?.eksConfig?.kubernetesVersion || '';
    } else {
      if (this.isImport) {
        this.normanCluster = await store.dispatch('rancher/create', { type: NORMAN.CLUSTER, ...cloneDeep(DEFAULT__IMPORT_CLUSTER) }, { root: true });
        this.config = this.normanCluster.eksConfig as EKSConfig;
      } else {
        this.normanCluster = await store.dispatch('rancher/create', { type: NORMAN.CLUSTER, ...DEFAULT_CLUSTER }, { root: true });
      }
      if (!this.$store.getters['auth/principalId'].includes('local://')) {
        this.normanCluster.annotations[CREATOR_PRINCIPAL_ID] = this.$store.getters['auth/principalId'];
      }
    }

    if (!this.isImport) {
      if (!this.normanCluster.eksConfig) {
        this.normanCluster['eksConfig'] = { ...DEFAULT_EKS_CONFIG } as any as EKSConfig;
      }
      if (!this.normanCluster.fleetAgentDeploymentCustomization) {
        this.normanCluster['fleetAgentDeploymentCustomization'] = {};
      }
      if (!this.normanCluster.clusterAgentDeploymentCustomization) {
        this.normanCluster['clusterAgentDeploymentCustomization'] = {};
      }
      this.config = this.normanCluster.eksConfig as EKSConfig;

      if ((!this.config.nodeGroups || !this.config.nodeGroups.length) && this.mode === _CREATE) {
        // Apply smart defaults if in PLG mode
        this.applySmartDefaults();
        if (this.nodeGroups.length === 0) {
          this.config['nodeGroups'] = [{ ...DEFAULT_NODE_GROUP_CONFIG, nodegroupName: 'group1' }];
        } else {
          this.config['nodeGroups'] = this.nodeGroups;
        }
      }
      if (this.config.nodeGroups) {
        this.nodeGroups = this.config.nodeGroups;
      } else {
        this.config['nodeGroups'] = this.nodeGroups;
      }
    }
    if (this.mode !== _VIEW) {
      this.fetchInstanceTypes();
      this.fetchLaunchTemplates();
      this.fetchServiceRoles();
      this.fetchSshKeys();
    }
  },

  data() {
    // if we are registering a new EKS cluster most of the form is hidden
    const isImport = this.$route?.query?.mode === 'import';
    const rkeType = this.$route?.query?.rkeType;

    return {
      isImport,
      rkeType,
      cloudCredentialId: '',
      normanCluster: { name: '' } as unknown as NormanCluster,
      nodeGroups: [] as EKSNodeGroup[],
      config: {} as EKSConfig,
      membershipUpdate: {} as { newBindings: any[], removedBindings: any[], save: Function },
      originalVersion: '',
      showAdvancedSettings: false,
      fvFormRuleSets: isImport ? [{
        path: 'name',
        rules: ['nameRequired'],
      },
      // eks cluster name when choosing a cluster to register
      {
        path: 'displayName',
        rules: ['displayNameRequired'],
      }
      ] : [{
        path: 'name',
        rules: ['nameRequired'],
      },
      {
        path: 'nodegroupNames',
        rules: ['nodeGroupNamesRequired', 'nodeGroupNamesUnique']

      },
      {
        path: 'maxSize',
        rules: ['maxSize']
      },
      {
        path: 'minSize',
        rules: ['minSize']
      },
      {
        path: 'desiredSize',
        rules: ['desiredSize']
      },
      {
        path: 'subnets',
        rules: ['subnets']
      },
      {
        path: 'instanceType',
        rules: ['instanceType']
      },
      {
        path: 'diskSize',
        rules: ['diskSize']
      },
      {
        path: 'networking',
        rules: ['publicPrivateAccess']
      },
      {
        path: 'minMaxDesired',
        rules: ['minMaxDesired', 'minLessThanMax']
      },
      {
        path: 'nodeGroupsRequired',
        rules: ['nodeGroupsRequired']
      }
      ],

      loadingInstanceTypes: false,
      loadingLaunchTemplates: false,
      loadingSshKeyPairs: false,
      loadingIam: false,
      iamInfo: [] as AWS.IamRole[],
      ec2Roles: [] as AWS.IamRole[],
      eksRoles: [] as AWS.IamRole[],
      sshKeyPairs: [] as string[],
      instanceTypes: [],
      launchTemplates: [],
      errors: [] as any[]
    };
  },

  created() {
    const registerAfterHook: Function = this.registerAfterHook;
    const registerBeforeHook: Function = this.registerBeforeHook;

    registerAfterHook(this.saveRoleBindings, 'save-role-bindings');
    registerBeforeHook(this.ensurePLGDefaultsBeforeSave, 'ensure-plg-defaults');

    // Initialize PLG mode based on current context
    this.initializeModeContext();

    // Apply smart defaults on creation
    if (this.mode === _CREATE && this.isPLGMode) {
      this.applySmartDefaults();
    }
  },

  mounted() {
    // Ensure mode context is properly set after component is fully mounted
    this.$nextTick(() => {
      this.initializeModeContext();
    });
  },



  watch: {
    isPLGMode: {
      handler(newVal: boolean, oldVal: boolean) {
        if (newVal !== oldVal) {
          this.handlePLGModeChange(newVal);
        }
      },
      immediate: false
    },

    'iamInfo'(neu: AWS.IamRole[]) {
      const ec2Roles = [] as AWS.IamRole[];
      const eksRoles = [] as AWS.IamRole[];
      const allRoles = neu;

      (allRoles || []).forEach((role: AWS.IamRole) => {
        const policy = JSON.parse(decodeURIComponent(role.AssumeRolePolicyDocument));
        const statement = policy.Statement;

        statement.forEach((doc: { Principal: { Service: string, EKS: boolean } }) => {
          const principal = doc.Principal;

          if (principal) {
            const service = principal.Service;

            if (service && service.includes('ec2.amazonaws.com') && !ec2Roles.find((r) => r.RoleId === role.RoleId) && !role.RoleName.match(/^rancher-managed/)) {
              ec2Roles.push(role);
            }
            if (service && (service.includes('eks.amazonaws') || service.includes('EKS')) && !eksRoles.find((r) => r.RoleId === role.RoleId)) {
              eksRoles.push(role);
            } else if (principal.EKS) {
              eksRoles.push(role);
            }
          }
        });
      });

      this.ec2Roles = ec2Roles;
      this.eksRoles = eksRoles;
      this.loadingIam = false;
    },

    'config.kubernetesVersion'(neu) {
      this.nodeGroups.forEach((group: EKSNodeGroup) => {
        if (group._isNew) {
          group['version'] = neu;
        }
      });
    }
  },

  computed: {
    ...mapGetters({ t: 'i18n/t' }),

    isPLGMode() {
      // Use effective PLG mode that considers toggle availability
      return this.$store.getters['saasAdmin/getEffectivePLGMode'](this.$route);
    },

    isPLGToggleAvailable() {
      // Check if PLG toggle is available in current context
      return this.$store.getters['saasAdmin/isPLGToggleAvailable'](this.$route);
    },

    fetchState(): { pending: boolean } {
      return this.$fetchState;
    },

    isRKE2Mode(): boolean {
      return this.rkeType === 'rke2';
    },

    wizardSteps(): any[] {
      const steps = [];

      // Step 1: Cluster Basics
      steps.push({
        name: 'cluster-basics',
        label: 'Cluster Basics',
        subtext: 'Name and AWS credentials',
        ready: !!this.normanCluster?.name && !!this.config?.amazonCredentialSecret && !!this.config?.region,
        hidden: false
      });

      // Step 2: Node Configuration
      steps.push({
        name: 'node-configuration',
        label: 'Node Configuration',
        subtext: 'Configure worker nodes',
        ready: this.nodeGroups.length > 0 && this.nodeGroups[0].instanceType && this.nodeGroups[0].nodegroupName,
        hidden: false
      });

      // Step 3: Review & Create
      steps.push({
        name: 'review',
        label: 'Review & Create',
        subtext: 'Review configuration',
        ready: true,
        hidden: false
      });

      return steps;
    },

    fvExtraRules(): { [key: string]: Function } {
      let out: any = {};

      if (this.hasCredential) {
        out = {
          displayNameRequired: EKSValidators.displayNameRequired(this),
          nameRequired: EKSValidators.clusterNameRequired(this),
          nodeGroupNamesRequired: EKSValidators.nodeGroupNamesRequired(this),
          nodeGroupNamesUnique: EKSValidators.nodeGroupNamesUnique(this),
          maxSize: EKSValidators.maxSize(this),
          minSize: EKSValidators.minSize(this),
          diskSize: EKSValidators.diskSize(this),
          instanceType: EKSValidators.instanceType(this),
          desiredSize: EKSValidators.desiredSize(this),
          subnets: EKSValidators.subnets(this),
          publicPrivateAccess: EKSValidators.publicPrivateAccess(this),
          minMaxDesired: EKSValidators.minMaxDesired(this),
          minLessThanMax: EKSValidators.minLessThanMax(this),
        };
        if (!this.config?.imported) {
          out.nodeGroupsRequired = EKSValidators.nodeGroupsRequired(this);
        }
      }

      return out;
    },

    // upstreamSpec will be null if the user created a cluster with some invalid options such that it ultimately fails to create anything in aks
    // this allows them to go back and correct their mistakes without re-making the whole cluster
    isNewOrUnprovisioned(): boolean {
      return this.mode === _CREATE || !this.normanCluster?.eksStatus?.upstreamSpec;
    },

    isEdit(): boolean {
      return this.mode === _CREATE || this.mode === _EDIT;
    },

    doneRoute(): string {
      return this.value?.listLocation?.name;
    },

    hasCredential(): boolean {
      return !!this.config?.amazonCredentialSecret;
    },

    clusterId(): string | null {
      return this.value?.id || null;
    },

    // used to display VPC/subnet information in the networking tab for imported clusters and clusters with the 'create a vpc and subnets automatically' option selected
    statusSubnets(): string[] {
      return this.normanCluster?.eksStatus?.subnets || [];
    },

    canManageMembers(): boolean {
      return canViewClusterMembershipEditor(this.$store);
    },

    CREATE(): string {
      return _CREATE;
    },

    VIEW(): string {
      return _VIEW;
    },

    groupedInstanceTypes(): { [key: string]: AWS.InstanceType[] } {
      const out: { [key: string]: AWS.InstanceType[] } = {};

      this.instanceTypes.forEach((type: AWS.InstanceType) => {
        if (out[type.groupLabel]) {
          out[type.groupLabel].push(type);
        } else {
          out[type.groupLabel] = [type];
        }
      });

      return out;
    },

    instanceTypeOptions(): AWS.InstanceTypeOption[] {
      const out: AWS.InstanceTypeOption[] = [];

      Object.keys(this.groupedInstanceTypes).forEach((groupLabel: string) => {
        const instances = this.groupedInstanceTypes[groupLabel];
        const groupOption = { label: groupLabel, kind: 'group' };
        const instanceTypeOptions = instances.map((instance: AWS.InstanceType) => {
          return {
            value: instance.apiName,
            label: instance.label,
            group: instance.groupLabel
          };
        });

        out.push(groupOption);
        out.push(...instanceTypeOptions);
      });

      return out;
    },

    recommendedInstanceTypes(): AWS.InstanceTypeOption[] {
      // Filter to show only recommended instance types for PLG users
      return this.instanceTypeOptions.filter((opt: AWS.InstanceTypeOption) => {
        if (opt.kind === 'group') {
          const label = opt.label || '';
          return label.includes('General Purpose') || label.includes('Compute Optimized');
        }
        const value = opt.value || '';
        // Recommend t3/t4g series for cost-effectiveness
        return value.startsWith('t3.') || value.startsWith('t4g.') ||
          value.startsWith('m5.') || value.startsWith('m6i.');
      });
    },

    spotInstanceTypeOptions(): AWS.InstanceTypeOption[] {
      const out: AWS.InstanceTypeOption[] = [];

      Object.keys(this.groupedInstanceTypes).forEach((groupLabel: string) => {
        const instances = this.groupedInstanceTypes[groupLabel];
        const groupOption = { label: groupLabel, kind: 'group' };
        const instanceTypeOptions = instances.reduce((spotInstances: AWS.InstanceTypeOption[], instance: AWS.InstanceType) => {
          if (!(instance.supportedUsageClasses || []).includes('spot')) {
            return spotInstances;
          }
          const opt = {
            value: instance.apiName,
            label: instance.label,
            group: instance.groupLabel
          };

          spotInstances.push(opt);

          return spotInstances;
        }, []);

        if (instanceTypeOptions.length) {
          out.push(groupOption);
          out.push(...instanceTypeOptions);
        }
      });

      return out;
    },
  },

  methods: {
    /**
     * Initialize PLG mode context based on current route and component state
     */
    initializeModeContext() {
      // Initialize PLG mode based on current context
      this.$store.dispatch('saasAdmin/initializePLGMode', this.$route);

      // Log context for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('PLG Mode Context:', {
          mode: this.mode,
          route: this.$route.name,
          toggleAvailable: this.isPLGToggleAvailable,
          effectiveMode: this.isPLGMode
        });
      }
    },

    handlePLGModeChange(isPlgMode: boolean) {
      // Save current wizard step when switching away from PLG mode
      if (!isPlgMode && this.$refs.wizard) {
        const wizard = this.$refs.wizard as any;
        if (wizard.activeStep) {
          this.$store.dispatch('saasAdmin/setLastWizardStep', wizard.activeStep.name);
        }
      }

      // Handle PLG mode changes from the toggle panel
      if (isPlgMode && this.mode === _CREATE) {
        this.applySmartDefaults();

        // Restore the last wizard step when switching back to PLG mode
        this.$nextTick(() => {
          const lastWizardStep = this.$store.getters['saasAdmin/lastWizardStep'];
          if (lastWizardStep && this.$refs.wizard) {
            const wizard = this.$refs.wizard as any;
            const stepIndex = this.wizardSteps.findIndex(step => step.name === lastWizardStep);
            if (stepIndex >= 0) {
              wizard.goToStep(stepIndex + 1);
            }
          }
        });
      }
      // Reset advanced settings when switching to PLG mode
      if (isPlgMode) {
        this.showAdvancedSettings = false;
      }
    },

    enableAdvancedMode() {
      // Use context-aware action to set PLG mode to false (advanced mode)
      this.$store.dispatch('saasAdmin/setPLGModeWithContext', {
        isPLGMode: false,
        route: this.$route
      });
      // Also set local showAdvancedSettings to true for immediate UI update
      this.showAdvancedSettings = true;
    },



    applySmartDefaults(): void {
      if (this.isPLGMode && this.mode === _CREATE) {
        // Note: kubernetesVersion is automatically set to the latest available version
        // by the Config component's versionOptions watcher, so we don't need to set it here

        // Set default region if not set
        if (!this.config.region) {
          this.config['region'] = DEFAULT_REGION;
        }

        // Ensure EKS config has required defaults
        if (!this.config.publicAccess && !this.config.privateAccess) {
          this.config['publicAccess'] = true;
          this.config['privateAccess'] = false;
        }

        // Set default logging types if not configured
        if (!this.config.loggingTypes) {
          this.config['loggingTypes'] = [];
        }

        // Apply smart defaults for PLG users - create default node group if none exist
        if (!this.nodeGroups || this.nodeGroups.length === 0) {
          this.nodeGroups = [{
            ...DEFAULT_NODE_GROUP_CONFIG,
            nodegroupName: 'default-pool',
            instanceType: 't3.medium', // Good balance of cost/performance
            minSize: 2,
            maxSize: 10,
            desiredSize: 3,
            // Version will be set by the 'config.kubernetesVersion' watcher when the Config component
            // automatically sets the latest available version
            version: this.config.kubernetesVersion || undefined
          }];
        }

        // Ensure all node groups have required fields and version set
        this.nodeGroups.forEach((group: EKSNodeGroup) => {
          if (!group.nodegroupName) {
            group.nodegroupName = 'default-pool';
          }
          if (!group.instanceType) {
            group.instanceType = 't3.medium';
          }
          // Version will be automatically set by the 'config.kubernetesVersion' watcher
          // when the Config component loads the latest available version
          if (!group.version && this.config.kubernetesVersion) {
            group['version'] = this.config.kubernetesVersion;
          }
        });
      }
    },

    setClusterName(name: string): void {
      this.normanCluster['name'] = name;
      if (!this.isImport) {
        this.config['displayName'] = name;
      }
    },

    onMembershipUpdate(update: { newBindings: any[], removedBindings: any[], save: Function }): void {
      this['membershipUpdate'] = update;
    },

    async saveRoleBindings(): Promise<void> {
      if (this.membershipUpdate.save) {
        await this.membershipUpdate.save(this.normanCluster.id);
      }
    },

    // only save values that differ from upstream aks spec - see diffUpstreamSpec comments for details
    removeUnchangedConfigFields(): void {
      const upstreamConfig = this.normanCluster?.eksStatus?.upstreamSpec;

      if (upstreamConfig) {
        const diff = diffUpstreamSpec(upstreamConfig, this.config);

        this.normanCluster['eksConfig'] = diff;
      }
    },

    /**
     * Before save hook - called by CreateEditView mixin before saving
     */
    async ensurePLGDefaultsBeforeSave(): Promise<void> {
      // Ensure PLG mode defaults are applied before saving
      if (this.isPLGMode && this.mode === _CREATE) {
        this.ensurePLGDefaults();
      }

      // Ensure node groups are properly configured
      this.config['nodeGroups'] = this.nodeGroups;

      // Remove unchanged fields for edit mode
      if (!this.isNewOrUnprovisioned) {
        this.removeUnchangedConfigFields();
      }
    },

    /**
     * Ensures all required defaults are set for PLG mode
     */
    ensurePLGDefaults(): void {
      // Apply smart defaults - this now handles all the default setting logic
      this.applySmartDefaults();

      // Note: All default setting logic (kubernetesVersion, region, node groups, etc.)
      // is now consolidated in applySmartDefaults() to eliminate duplication
    },

    async actuallySave(): Promise<void> {
      await this.normanCluster.save();

      return await this.normanCluster.waitForCondition('InitialRolesPopulated');
    },

    // fires when the 'cancel' button is pressed while the user is creating a new cloud credential
    cancelCredential(): void {
      if (this.$refs.cruresource) {
        (this.$refs.cruresource as any).emitOrRoute();
      }
    },

    updateRegion(e: string) {
      this.config['region'] = e;
      this.fetchInstanceTypes();
      this.fetchLaunchTemplates();
      this.fetchServiceRoles();
      this.fetchSshKeys();
    },

    updateCredential(e: string) {
      this.config['amazonCredentialSecret'] = e;
      this.fetchInstanceTypes();
      this.fetchLaunchTemplates();
      this.fetchServiceRoles();
      this.fetchSshKeys();
    },

    removeGroup(i: number) {
      const group = this.nodeGroups[i];

      removeObject(this.nodeGroups, group);
    },

    addGroup() {
      let nextDefaultSuffix = this.nodeGroups.length + 1;

      while (this.nodeGroups.find((group) => group.nodegroupName === `group${nextDefaultSuffix}`)) {
        nextDefaultSuffix++;
      }
      this.nodeGroups.push({
        ...DEFAULT_NODE_GROUP_CONFIG, nodegroupName: `group${nextDefaultSuffix}`, version: this.config?.kubernetesVersion
      });
    },

    async fetchInstanceTypes() {
      const store = this.$store as Store<any>;

      if (!this.config.region || !this.config.amazonCredentialSecret) {
        return;
      }
      this.loadingInstanceTypes = true;
      try {
        const ec2Client = await store.dispatch('aws/ec2', { region: this.config.region, cloudCredentialId: this.config.amazonCredentialSecret });

        this.instanceTypes = await store.dispatch('aws/describeInstanceTypes', { client: ec2Client });
      } catch { }
      this.loadingInstanceTypes = false;
    },

    async fetchLaunchTemplates() {
      const store = this.$store as Store<any>;

      if (!this.config.region || !this.config.amazonCredentialSecret) {
        return;
      }
      this.loadingLaunchTemplates = true;
      try {
        const ec2Client = await store.dispatch('aws/ec2', { region: this.config.region, cloudCredentialId: this.config.amazonCredentialSecret });

        this.launchTemplates = await store.dispatch('aws/depaginateList', {
          client: ec2Client, cmd: 'describeLaunchTemplates', opt: { DryRun: false }
        });
      } catch (err: any) {
        const errors = this.errors as any[];

        errors.push(err);
      }
      this.loadingLaunchTemplates = false;
    },

    async fetchServiceRoles() {
      const { region, amazonCredentialSecret } = this.config;

      if (!region || !amazonCredentialSecret) {
        return;
      }
      this.loadingIam = true;
      const store = this.$store as Store<any>;
      const iamClient = await store.dispatch('aws/iam', { region, cloudCredentialId: amazonCredentialSecret });

      try {
        const res = await store.dispatch('aws/depaginateList', { client: iamClient, cmd: 'listRoles' });

        this.iamInfo = res;
      } catch (err: any) {
        const errors = this.errors as any[];

        errors.push(err);
      }
    },

    async fetchSshKeys() {
      const { region, amazonCredentialSecret } = this.config;

      if (!region || !amazonCredentialSecret) {
        return;
      }
      this.loadingSshKeyPairs = true;
      const store = this.$store as Store<any>;

      try {
        const ec2Client = await store.dispatch('aws/ec2', { region: this.config.region, cloudCredentialId: this.config.amazonCredentialSecret });

        const keyPairRes: { KeyPairs: { KeyName: string }[] } = await ec2Client.describeKeyPairs({ DryRun: false });

        this.sshKeyPairs = (keyPairRes.KeyPairs || []).map((key) => {
          return key.KeyName;
        }).sort();
      } catch (err: any) {
        const errors = this.errors as any[];

        errors.push(err);
      }
      this.loadingSshKeyPairs = false;
    },
  }

});
</script>

<template>
  <div class="crueks-container">
    <Loading v-if="fetchState.pending" />

    <template v-else>


      <div v-if="!isPLGMode || showAdvancedSettings" class="advanced-mode-container">

        <CruResource ref="cruresource" :resource="value" :mode="mode" :can-yaml="false" :done-route="doneRoute"
          :errors="fvUnreportedValidationErrors" :validation-passed="fvFormIsValid" @error="e => errors = e" @finish="save"
          @cancel="done">
          <div v-if="hasCredential" class="row mb-10">
            <div class="col span-6">
              <LabeledInput required label-key="eks.clusterName.label" :value="normanCluster.name" :mode="mode"
                :rules="fvGetAndReportPathRules('name')" data-testid="eks-name-input" @update:value="setClusterName" />
            </div>
            <div class="col span-6">
              <LabeledInput v-model:value="normanCluster.description" :mode="mode"
                label-key="nameNsDescription.description.label"
                :placeholder="t('nameNsDescription.description.placeholder')" />
            </div>
          </div>

          <AccountAccess :credential="config.amazonCredentialSecret" :mode="mode" :region="config.region"
            @cancel-credential="cancelCredential" @update-region="updateRegion" @update-credential="updateCredential"
            @error="e => errors.push(e)" />

          <div v-if="hasCredential" data-testid="crueks-form">
            <Accordion v-if="isImport" class="mb-20" :title="t('eks.accordionHeaders.cluster')" :open-initially="true">
              <Import v-model:cluster-name="config.displayName"
                v-model:enable-network-policy="normanCluster.enableNetworkPolicy"
                :credential="config.amazonCredentialSecret" :mode="mode" :region="config.region"
                :rules="{ displayName: fvGetAndReportPathRules('displayName') }" @error="e => errors.push(e)" />
            </Accordion>
            <template v-else>
              <div>
                <h3>{{ t('eks.nodeGroups.title') }}</h3>
              </div>
              <Tabbed class="mb-20" :side-tabs="true" :show-tabs-add-remove="mode !== VIEW" :use-hash="false"
                @removeTab="removeGroup($event)" @addTab="addGroup()">
                <Tab v-for="(node, i) in nodeGroups" :key="i" :label="node.nodegroupName || t('eks.nodeGroups.unnamed')"
                  :name="`${node.nodegroupName} ${i}`">
                  <NodeGroup v-model:node-role="node.nodeRole" v-model:launch-template="node.launchTemplate"
                    v-model:nodegroup-name="node.nodegroupName" v-model:ec2-ssh-key="node.ec2SshKey"
                    v-model:tags="node.tags" v-model:resource-tags="node.resourceTags" v-model:disk-size="node.diskSize"
                    v-model:image-id="node.imageId" v-model:instance-type="node.instanceType"
                    v-model:spot-instance-types="node.spotInstanceTypes" v-model:user-data="node.userData"
                    v-model:gpu="node.gpu" v-model:desired-size="node.desiredSize" v-model:min-size="node.minSize"
                    v-model:max-size="node.maxSize" v-model:request-spot-instances="node.requestSpotInstances"
                    v-model:labels="node.labels" v-model:version="node.version"
                    v-model:pool-is-upgrading="node._isUpgrading" :rules="{
                      nodegroupName: fvGetAndReportPathRules('nodegroupNames'),
                      maxSize: fvGetAndReportPathRules('maxSize'),
                      minSize: fvGetAndReportPathRules('minSize'),
                      desiredSize: fvGetAndReportPathRules('desiredSize'),
                      instanceType: fvGetAndReportPathRules('instanceType'),
                      diskSize: fvGetAndReportPathRules('diskSize'),
                      minMaxDesired: fvGetAndReportPathRules('minMaxDesired')
                    }" :cluster-version="config.kubernetesVersion" :original-cluster-version="originalVersion"
                    :region="config.region" :amazon-credential-secret="config.amazonCredentialSecret"
                    :is-new-or-unprovisioned="isNewOrUnprovisioned" :pool-is-new="node._isNew" :mode="mode"
                    :instance-type-options="instanceTypeOptions" :spot-instance-type-options="spotInstanceTypeOptions"
                    :launch-templates="launchTemplates" :ec2-roles="ec2Roles" :ssh-key-pairs="sshKeyPairs"
                    :loading-instance-types="loadingInstanceTypes" :loading-roles="loadingIam"
                    :loading-launch-templates="loadingLaunchTemplates" :loading-ssh-key-pairs="loadingSshKeyPairs"
                    :norman-cluster="normanCluster" @error="errors.push($event)" />
                </Tab>
              </Tabbed>
              <Accordion class="mb-20" :title="t('eks.accordionHeaders.cluster')" :open-initially="true">
                <Config v-model:kubernetes-version="config.kubernetesVersion"
                  v-model:enable-network-policy="normanCluster.enableNetworkPolicy"
                  v-model:ebs-c-s-i-driver="config.ebsCSIDriver" v-model:service-role="config.serviceRole"
                  v-model:kms-key="config.kmsKey" v-model:secrets-encryption="config.secretsEncryption"
                  v-model:tags="config.tags" :mode="mode" :config="config" :eks-roles="eksRoles"
                  :loading-iam="loadingIam" :original-version="originalVersion" data-testid="eks-config-section"
                  @error="errors.push($event)" />
              </Accordion>

              <Accordion class="mb-20" :title="t('eks.accordionHeaders.networking')" :open-initially="true">
                <Networking v-model:public-access="config.publicAccess" v-model:private-access="config.privateAccess"
                  v-model:public-access-sources="config.publicAccessSources" v-model:subnets="config.subnets"
                  v-model:security-groups="config.securityGroups" :mode="mode" :region="config.region"
                  :amazon-credential-secret="config.amazonCredentialSecret" :status-subnets="statusSubnets"
                  :rules="{ subnets: fvGetAndReportPathRules('subnets') }" />
              </Accordion>
              <Accordion class="mb-20" :title="t('eks.accordionHeaders.logging')" :open-initially="true">
                <Logging v-model:logging-types="config.loggingTypes" :mode="mode" :config="config" />
              </Accordion>
            </template>

            <Accordion class="mb-20" :title="t('eks.accordionHeaders.clusterAgent')">
              <AgentConfiguration v-model:value="normanCluster.clusterAgentDeploymentCustomization" :mode="mode"
                type="cluster" />
            </Accordion>
            <Accordion class="mb-20" :title="t('eks.accordionHeaders.fleetAgent')">
              <AgentConfiguration v-model:value="normanCluster.fleetAgentDeploymentCustomization" :mode="mode"
                type="fleet" />
            </Accordion>
            <Accordion class="mb-20" :title="t('members.memberRoles')">
              <Banner v-if="isEdit" color="info">
                {{ t('cluster.memberRoles.removeMessage') }}
              </Banner>
              <ClusterMembershipEditor v-if="canManageMembers" :mode="mode"
                :parent-id="normanCluster.id ? normanCluster.id : undefined" @membership-update="onMembershipUpdate" />
            </Accordion>
            <Accordion class="mb-20" :title="t('generic.labelsAndAnnotations')">
              <Labels v-model:value="normanCluster" :mode="mode" />
            </Accordion>
          </div>
          <template v-if="!hasCredential" #form-footer>
            <div><!-- Hide the outer footer --></div>
          </template>
        </CruResource>
      </div>

      <!-- PLG Wizard Mode -->
      <div v-else class="wizard-container">
        <!-- Hidden Config component to ensure kubernetes version selection logic runs -->
        <div style="display: none;">
          <Config v-model:kubernetes-version="config.kubernetesVersion"
            v-model:enable-network-policy="normanCluster.enableNetworkPolicy"
            v-model:ebs-c-s-i-driver="config.ebsCSIDriver" v-model:service-role="config.serviceRole"
            v-model:kms-key="config.kmsKey" v-model:secrets-encryption="config.secretsEncryption"
            v-model:tags="config.tags" :mode="mode" :config="config" :eks-roles="eksRoles"
            :loading-iam="loadingIam" :original-version="originalVersion"
            @error="errors.push($event)" />
        </div>

        <Wizard ref="wizard" :show-banner="false" :steps="wizardSteps" :mode="mode" :errors="errors"
          :edit-first-step="true" @finish="save" @cancel="cancelCredential">
          <!-- Step 1: Cluster Basics -->
          <template #cluster-basics>
            <Card class="wizard-card" :show-actions="false">
              <template #body>
                <div class="wizard-section">
                  <h3 class="mb-20">Let's create your EKS cluster</h3>
                  <p class="text-muted mb-20">First, we need a name for your cluster and AWS credentials.</p>

                  <div class="row mb-20">
                    <div class="col span-6">
                      <LabeledInput required label="Cluster Name" :value="normanCluster.name" :mode="mode"
                        :rules="fvGetAndReportPathRules('name')" placeholder="my-cluster" data-testid="eks-name-input"
                        @update:value="setClusterName" />
                    </div>
                    <div class="col span-6">
                      <LabeledInput v-model:value="normanCluster.description" :mode="mode"
                        label="Description (Optional)" placeholder="Production cluster for web services" />
                    </div>
                  </div>

                  <AccountAccess :credential="config.amazonCredentialSecret" :mode="mode" :region="config.region"
                    @cancel-credential="cancelCredential" @update-region="updateRegion"
                    @update-credential="updateCredential" @error="e => errors.push(e)" />
                </div>
              </template>
            </Card>
          </template>

          <!-- Step 2: Node Configuration -->
          <template #node-configuration>
            <Card class="wizard-card" :show-actions="false">
              <template #body>
                <div class="wizard-section">
                  <h3 class="mb-20">Configure your worker nodes</h3>
                  <p class="text-muted mb-20">We've selected recommended settings that work well for most workloads.</p>

                  <SimpleNodeGroup v-if="nodeGroups[0]" v-model="nodeGroups[0]"
                    :instance-type-options="instanceTypeOptions" :loading-instance-types="loadingInstanceTypes"
                    :mode="mode" :rules="{
                      instanceType: fvGetAndReportPathRules('instanceType'),
                      minSize: fvGetAndReportPathRules('minSize'),
                      maxSize: fvGetAndReportPathRules('maxSize'),
                      desiredSize: fvGetAndReportPathRules('desiredSize'),
                      diskSize: fvGetAndReportPathRules('diskSize'),
                      minMaxDesired: fvGetAndReportPathRules('minMaxDesired')
                    }" />
                </div>
              </template>
            </Card>
          </template>

          <!-- Step 3: Review & Create -->
          <template #review>
            <div class="wizard-review">
              <Card class="wizard-card mb-20" :show-actions="false">
                <template #body>
                  <div class="wizard-section">
                    <h3 class="mb-20">Review your cluster configuration</h3>
                    <ConfigSummary :norman-cluster="normanCluster" :config="config" :node-groups="nodeGroups"
                      :region="config.region" />
                  </div>
                  <!-- Advanced Settings Toggle -->
                  <Card v-if="!showAdvancedSettings" class="wizard-card advanced-toggle-card" :show-actions="false">
                    <template #body>
                      <div class="advanced-toggle">
                        <div class="advanced-info">
                          <h4>Need more control?</h4>
                          <p>Access advanced settings for networking, logging, IAM, and more.</p>
                        </div>
                        <button type="button" class="btn role-tertiary" @click="enableAdvancedMode">
                          <i class="icon icon-settings" />
                          Show Advanced Settings
                        </button>
                      </div>
                    </template>
                  </Card>
                </template>
              </Card>
            </div>
          </template>
        </Wizard>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.wizard-container {
  position: relative;
}

.advanced-mode-container {
  position: relative;
}





.wizard-card {
  margin-bottom: 20px;

  .wizard-section {
    padding: 25px;
    padding-bottom: 40px; // Add bottom padding for better scrolling in all wizard steps

    h3 {
      margin: 0 0 10px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--text-default);
    }

    .text-muted {
      color: var(--text-muted);
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.wizard-review {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  padding-right: 10px;
  padding-bottom: 40px; // Add bottom padding to ensure scrolling past the last card
}

.advanced-toggle-card {
  background: linear-gradient(135deg, var(--body-bg) 0%, var(--nav-bg) 100%);
  border: 2px dashed var(--border);

  .advanced-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;

    .advanced-info {
      margin-right: 10px;
      h4 {
        margin: 0 0 5px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-default);
      }

      p {
        margin: 0;
        color: var(--text-muted);
        font-size: 14px;
      }
    }

    .btn {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 16px;
      }
    }
  }
}

// Override wizard footer for better spacing
::v-deep .wizard {
  .controls-row {
    background: var(--body-bg);
    border-top: 1px solid var(--border);
    padding: 20px;
    margin: 0;
  }
}
</style>
