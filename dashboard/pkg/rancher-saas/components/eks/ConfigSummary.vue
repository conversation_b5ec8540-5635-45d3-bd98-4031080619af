<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { mapGetters } from 'vuex';
import { EKSConfig, EKSNodeGroup, NormanCluster } from '../../types';
import LabeledInput from '@components/Form/LabeledInput/LabeledInput.vue';
import Banner from '@components/Banner/Banner.vue';

export default defineComponent({
  name: 'ConfigSummary',

  components: {
    LabeledInput,
    Banner
  },

  props: {
    normanCluster: {
      type:     Object as PropType<NormanCluster>,
      required: true
    },
    config: {
      type:     Object as PropType<EKSConfig>,
      required: true
    },
    nodeGroups: {
      type:    Array as PropType<EKSNodeGroup[]>,
      default: () => []
    },
    region: {
      type:    String,
      default: ''
    }
  },

  computed: {
    ...mapGetters({ t: 'i18n/t' }),

    clusterName(): string {
      return (this as any).normanCluster?.name || (this as any).t('plg.wizard.configSummary.values.notSet');
    },

    awsRegion(): string {
      return (this as any).config?.region || (this as any).region || (this as any).t('plg.wizard.configSummary.values.notSet');
    },

    kubernetesVersion(): string {
      return (this as any).config?.kubernetesVersion || (this as any).t('plg.wizard.configSummary.values.latestStable');
    },

    totalNodes(): { min: number; max: number; desired: number } {
      const totals = (this as any).nodeGroups.reduce((acc: any, group: any) => {
        return {
          min:     acc.min + (group.minSize || 0),
          max:     acc.max + (group.maxSize || 0),
          desired: acc.desired + (group.desiredSize || 0)
        };
      }, { min: 0, max: 0, desired: 0 });

      return totals;
    },

    primaryNodeGroup(): EKSNodeGroup | null {
      return (this as any).nodeGroups[0] || null;
    },

    estimatedMonthlyCost(): string {
      // Simple cost estimation
      const costMap: Record<string, number> = {
        't3.small':   15,
        't3.medium':  30,
        't3.large':   60,
        't3.xlarge':  120,
        't4g.small':  12,
        't4g.medium': 24,
        't4g.large':  48,
        'm5.large':   70,
        'm5.xlarge':  140,
        'm6i.large':  70,
        'm6i.xlarge': 140,
      };

      let totalCost = 0;
      (this as any).nodeGroups.forEach((group: any) => {
        const instanceCost = group.instanceType ? costMap[group.instanceType] || 50 : 50;
        totalCost += instanceCost * (group.desiredSize || 2);
      });

      // Add EKS control plane cost ($0.10/hour = ~$73/month)
      totalCost += 73;

      return `$${totalCost}`;
    },

    networkingMode(): string {
      const config = (this as any).config;
      const t = (this as any).t;
      if (config?.publicAccess && !config?.privateAccess) {
        return t('plg.wizard.configSummary.values.publicNetwork');
      } else if (!config?.publicAccess && config?.privateAccess) {
        return t('plg.wizard.configSummary.values.privateNetwork');
      } else if (config?.publicAccess && config?.privateAccess) {
        return t('plg.wizard.configSummary.values.publicPrivateNetwork');
      }
      return t('plg.wizard.configSummary.values.defaultPublicNetwork');
    }
  }
});
</script>

<template>
  <div class="config-summary">
    <div class="summary-section">
      <h3>
        <i class="icon icon-cluster" />
        {{ t('plg.wizard.configSummary.sections.cluster') }}
      </h3>

      <div class="summary-grid">
        <LabeledInput
          :label="t('plg.wizard.configSummary.fields.clusterName')"
          :value="clusterName"
          mode="view"
          class="summary-item"
        />

        <LabeledInput
          :label="t('plg.wizard.configSummary.fields.awsRegion')"
          :value="awsRegion"
          mode="view"
          class="summary-item"
        />

        <LabeledInput
          :label="t('plg.wizard.configSummary.fields.kubernetesVersion')"
          :value="kubernetesVersion"
          mode="view"
          class="summary-item"
        />

        <LabeledInput
          :label="t('plg.wizard.configSummary.fields.networkAccess')"
          :value="networkingMode"
          mode="view"
          class="summary-item"
        />
      </div>
    </div>

    <div class="summary-section mt-20">
      <h3>
        <i class="icon icon-nodes" />
        {{ t('plg.wizard.configSummary.sections.nodes') }}
      </h3>

      <div
        v-if="primaryNodeGroup"
        class="node-summary"
      >
        <div class="summary-grid">
          <LabeledInput
            :label="t('plg.wizard.configSummary.fields.instanceType')"
            :value="primaryNodeGroup.instanceType"
            mode="view"
            class="summary-item"
          />

          <LabeledInput
            :label="t('plg.wizard.configSummary.fields.diskSize')"
            :value="t('plg.wizard.configSummary.values.diskSizeUnit', { size: primaryNodeGroup.diskSize })"
            mode="view"
            class="summary-item"
          />

          <LabeledInput
            :label="t('plg.wizard.configSummary.fields.autoScaling')"
            :value="t('plg.wizard.configSummary.values.autoScalingRange', { min: totalNodes.min, max: totalNodes.max })"
            mode="view"
            class="summary-item"
          />

          <LabeledInput
            :label="t('plg.wizard.configSummary.fields.initialSize')"
            :value="t('plg.wizard.configSummary.values.nodeCount', { count: totalNodes.desired })"
            mode="view"
            class="summary-item"
          />
        </div>
      </div>

      <div
        v-if="nodeGroups.length > 1"
        class="mt-10"
      >
        <Banner color="info">
          <p>{{ t('plg.wizard.configSummary.values.nodeGroupsConfigured', { count: nodeGroups.length }) }}</p>
        </Banner>
      </div>
    </div>

    <div class="summary-section mt-20 cost-section">
      <h3>
        <i class="icon icon-dollar" />
        {{ t('plg.wizard.configSummary.sections.cost') }}
      </h3>

      <div class="cost-breakdown">
        <div class="cost-main">
          <span class="cost-label">{{ t('plg.wizard.configSummary.fields.monthlyCost') }}</span>
          <span class="cost-value">{{ estimatedMonthlyCost }}</span>
        </div>
        <p class="cost-disclaimer">
          {{ t('plg.wizard.configSummary.fields.costDisclaimer') }}
        </p>
      </div>
    </div>

    <div class="summary-section mt-20">
      <Banner
        color="success"
        class="ready-banner"
      >
        <div class="ready-content">
          <i class="icon icon-checkmark icon-2x" />
          <div>
            <h4>{{ t('plg.wizard.configSummary.readyBanner.title') }}</h4>
            <p>{{ t('plg.wizard.configSummary.readyBanner.subtitle') }}</p>
            <ul>
              <li v-for="feature in t('plg.wizard.configSummary.readyBanner.features')" :key="feature">
                {{ feature }}
              </li>
            </ul>
          </div>
        </div>
      </Banner>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.config-summary {
  .summary-section {
    padding: 20px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);

    h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
      color: var(--text-default);

      i {
        color: var(--primary);
      }
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .cost-section {
    background: linear-gradient(135deg, var(--body-bg) 0%, var(--nav-bg) 100%);
  }

  .cost-breakdown {
    .cost-main {
      display: flex;
      align-items: baseline;
      gap: 15px;
      margin-bottom: 10px;

      .cost-label {
        font-size: 16px;
        color: var(--text-muted);
      }

      .cost-value {
        font-size: 32px;
        font-weight: 600;
        color: var(--success);
      }
    }

    .cost-disclaimer {
      font-size: 12px;
      color: var(--text-muted);
      font-style: italic;
      margin: 0;
    }
  }

  .ready-banner {
    .ready-content {
      display: flex;
      gap: 20px;
      align-items: flex-start;

      i {
        color: var(--success);
        flex-shrink: 0;
      }

      h4 {
        margin: 0 0 10px 0;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0 0 10px 0;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin: 5px 0;
        }
      }
    }
  }
}
</style>
